# Example configurations for ConcatModulator and FiLMModulator

# Example 1: ConcatModulator with default settings
concat_modulator_basic:
  model_type: v1
  frontend:
    type: da
    params:
      n_basis: 8
      tau: 0.1
  convnet:
    type: densenet
    params:
      growth_rate: 8
      num_blocks: 3
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: depthwise
          dim: 3
          kernel_size: [3, 5, 5]
          padding: [1, 2, 2]
        norm_type: rms
        act_type: mish
        pool_params: {}
  modulator:
    type: concat
    params:
      behavior_dim: 2
      encoder_params:
        type: mlp
        dims: [64, 128, 64]  # Output will be 64 channels
        activation: gelu
        bias: true
        residual: true
        dropout: 0.1
        last_layer_activation: false
  recurrent:
    type: convgru
    params:
      hidden_dim: 32
  readout:
    type: gaussian
    params:
      n_units: 100

# Example 2: FiLMModulator with custom settings
film_modulator_custom:
  model_type: v1
  frontend:
    type: da
    params:
      n_basis: 8
      tau: 0.1
  convnet:
    type: densenet
    params:
      growth_rate: 8
      num_blocks: 3
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: depthwise
          dim: 3
          kernel_size: [3, 5, 5]
          padding: [1, 2, 2]
        norm_type: rms
        act_type: mish
        pool_params: {}
  modulator:
    type: film
    params:
      behavior_dim: 4  # 4 behavioral variables
      encoder_params:
        type: mlp
        dims: [128, 256, 128]  # Hidden layers, output will be 128
        activation: silu
        bias: true
        residual: true
        dropout: 0.05
        last_layer_activation: false
  recurrent:
    type: convlstm
    params:
      hidden_dim: 64
  readout:
    type: gaussian
    params:
      n_units: 200

# Example 3: ConcatModulator with minimal configuration (uses defaults)
concat_modulator_minimal:
  model_type: v1
  frontend:
    type: conv
    params:
      out_channels: 16
  convnet:
    type: vanilla
    params:
      layer_configs:
        - out_channels: 32
          conv_params:
            kernel_size: 3
            padding: 1
  modulator:
    type: concat
    params:
      behavior_dim: 2
      # encoder_params will use defaults:
      # dims: [64, 64], activation: gelu, bias: true, residual: true
  recurrent:
    type: none
  readout:
    type: linear
    params:
      n_units: 50

# Example 4: FiLMModulator with performance-optimized settings
film_modulator_performance:
  model_type: v1
  frontend:
    type: da
    params:
      n_basis: 12
      tau: 0.08
  convnet:
    type: resnet
    params:
      layer_configs:
        - out_channels: 64
          conv_params:
            kernel_size: [3, 5, 5]
            stride: [1, 2, 2]
        - out_channels: 128
          conv_params:
            kernel_size: [3, 3, 3]
            stride: [1, 2, 2]
  modulator:
    type: film
    params:
      behavior_dim: 3
      encoder_params:
        type: mlp
        dims: [192, 256, 192]  # Multiples of 64 for performance
        activation: gelu
        bias: true
        residual: true
        dropout: 0.0
        last_layer_activation: false
  recurrent:
    type: convgru
    params:
      hidden_dim: 128
  readout:
    type: gaussian
    params:
      n_units: 500
