#!/usr/bin/env python
"""
Example script for model loading from the registry

This script demonstrates:
1. Loading a model from a registry
2. Run model on a batch
3. Compute and visualize Jacobians to analyze instantaneous receptive fields
4. Analyze how receptive fields change dynamically based on input
"""
#%%
import torch
from torch.utils.data import DataLoader
from torch.func import jacrev, vmap
import time
import torch.cuda.amp as amp  # For mixed precision

import lightning as pl

from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize

from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1 import get_session
from tqdm import tqdm
# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available
device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Global settings
SAVE_FIGURES = True  # Set to True to save figures to disk
DISPLAY_FIGURES = True  # Set to True to display figures inline

# Create a directory for saving results (only used if SAVE_FIGURES is True)
results_dir = Path("results/jacobian_analysis")
if SAVE_FIGURES:
    results_dir.mkdir(parents=True, exist_ok=True)

# Import animation tools for dynamic visualizations
from matplotlib import animation
from IPython.display import HTML
import time


#%% Load model
data_config_path = "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/Allen_2022_04_13_eyevel_16_lags.yaml"
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
registry = ModelRegistry(registry_dir)

sess = get_session('Allen', '2022-04-13')

# manually specify a model
model_name = 'dense_gaussian'
if model_name == 'conv_gru':
    # convgru
    best_model =registry.load_model('v1_da_dense_convgru_gaussian_epoch_25', map_location='cpu')
elif model_name == 'dense_gaussian':
    # densegaussian
    best_model = registry.load_model('v1_da_dense_gaussian_epoch_09', map_location='cpu')

model = best_model.model.to(device)

# prepared_data = prepare_data(model_entry['dataset_info'])
import yaml
with open(data_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

dataset_config['keys_lags']['eyepos'] = 0 #list(range(16))
train_data, val_data, dataset_config = prepare_data(dataset_config)

get_inds = lambda dset,x: dset.inds[np.where(dset.inds[:,0]==x)[0]]
gaborium_inds = get_inds(val_data, 0)
backimage_inds = get_inds(val_data, 1)

#%%

#%%
import os
num_workers = os.cpu_count()//2
batch_size = 256

data_loader = DataLoader(
    val_data, batch_size=batch_size, shuffle=False, num_workers=num_workers)

pred = []
robs = []
for batch in tqdm(data_loader):
    r = model(batch['stim'].to(device))
    pred.append(r.detach().cpu())
    robs.append(batch['robs'])

pred = torch.cat(pred, dim=0)
robs = torch.cat(robs, dim=0)

#%%
from DataYatesV1.utils.modeling.eval import calc_poisson_bits_per_spike

bps = calc_poisson_bits_per_spike(pred, robs)
bps = bps.detach().cpu().numpy()

corr_coefs = torch.zeros(pred.shape[1])
for i in range(pred.shape[1]):
    corr_coefs[i] = torch.corrcoef(torch.stack([pred[:, i], robs[:, i]]))[0, 1]

fig, axs = plt.subplots(1, 2, figsize=(10, 5))
axs[0].plot(bps, '-o')
axs[0].set_ylabel('Bits per spike')
axs[0].set_xlabel('Neuron')
# ax = axs[0].twinx()
# ax.plot(corr_coefs, '-o', color='r')
# ax.set_ylabel('Correlation coefficient')
# ax.set_xlabel('Neuron')

axs[1].plot(corr_coefs, '-o')
axs[1].set_ylabel('Correlation coefficient')
axs[1].set_xlabel('Neuron')

plt.tight_layout()

plt.figure()
plt.plot(corr_coefs, bps, 'o')
plt.xlabel('Correlation coefficient')
plt.ylabel('Bits per spike')

good_units = [int(i) for i in torch.where((corr_coefs > 0.15) & (bps > 0.25))[0]]

print(f'Found {len(good_units)} good units')

#%% Find saccade times
import json
saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
saccade_times = torch.sort(torch.tensor([s['start_time'] for s in saccades])).values.numpy()
saccade_times = saccade_times[np.diff(saccade_times, prepend=0) > 0.1]
_ = plt.hist(np.diff(saccade_times, prepend=0), np.linspace(0, 1, 100))
plt.xlabel('Time (s)')
plt.ylabel('Count')
plt.title('Saccade ISI distribution')

#%%
dset_id = 1
inds = np.where(val_data.inds[:,0]==dset_id)[0] # indices that map to this dataset
t_bins = val_data.dsets[dset_id]['t_bins'][val_data.dset_inds[dset_id]]

sacc_ind = np.digitize(saccade_times, t_bins)
valid_saccades = (sacc_ind > 0) & (sacc_ind < len(t_bins))

# and saccade isn't within 100ms of the next saccade
valid_saccades[:-1] = valid_saccades[:-1] & (np.diff(sacc_ind) > 20)

saccade_times = saccade_times[valid_saccades]
sacc_ind = sacc_ind[valid_saccades]

saccade_indices = inds[sacc_ind - 1]

valid_saccades = np.ones(len(saccade_indices), dtype=bool)
for i in range(len(saccade_indices)-1):
    bind = np.arange(saccade_indices[i]-10, saccade_indices[i+1])
    batch = val_data[bind]
    if batch['dfs'].mean() < 0.99:
        valid_saccades[i] = False
        print(f"Saccade {i} has {batch['dfs'].mean()} dfs")
    
       

#%% check these are valid saccades
n_sample = 12
fig, axs = plt.subplots(n_sample//4, 4, figsize=(15, 5))
for ii, i in enumerate(np.random.choice(len(saccade_indices), n_sample, replace=False)):
    bind = np.arange(-10, 100) + saccade_indices[i]
    batch = val_data[bind]
    axs[ii//4, ii%4].plot(batch['eyepos'])
    axs[ii//4, ii%4].axvline(10, color='k', linestyle='--')
    ax = axs[ii//4, ii%4].twinx()
    ax.plot(batch['dfs'], color='k')

#%% plot saccade triggered average
robs = 0
window_size = 100
prewin = 10
Nsac = len(saccade_indices)
n_units = val_data.dsets[dset_id]['robs'].shape[1]
rstack = np.zeros((Nsac, window_size+prewin, n_units))

from tqdm import tqdm
for ii, i in enumerate(tqdm(saccade_indices)):
    bind = np.arange(-prewin, window_size) + i
    rstack[ii] = val_data[bind]['robs'].numpy()
    robs += val_data[bind]['robs']


#%%
time_lags = np.arange(-prewin, window_size)
fig, axs = plt.subplots(1, 2, figsize=(10, 5))
_ = axs[0].plot(time_lags, robs/Nsac)
_ = axs[1].plot(time_lags, robs[:,good_units]/Nsac)
axs[0].set_title('All units')
axs[1].set_title('Good units')
axs[0].set_ylabel('Firing rate (spikes / bin)')
sac_list = np.argsort(rstack[:,prewin:,:].sum(1).sum(1))[::-1]

#%%
j = 0
# %%
%matplotlib inline
prewin = 10
j += 1
i = sac_list[j]
bind = np.arange(saccade_indices[i]-prewin, saccade_indices[i+1])
print(len(bind))
batch = val_data[bind]
plt.subplot(1,3,1)
plt.imshow(batch['stim'][0,0].cpu())
plt.subplot(1,3,2)
plt.imshow(batch['stim'][prewin*2,0].cpu())
plt.subplot(1,3,3)
plt.imshow(batch['stim'][-1,0].cpu())
plt.show()


batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}
stim = batch['stim'].to(device)
# stim.requires_grad = True

pred = model(stim)

plt.subplot(2,1,1)
_ = plt.imshow(batch['robs'][:,good_units].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.ylabel('Units')

plt.subplot(2,1,2)
# spikefn = lambda x: torch.poisson(x)
spikefn = lambda x: x
_ = plt.imshow(spikefn(pred[:,good_units]).detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.xlabel('Time (5ms bins)')
plt.ylabel('Units')

# #%%
# _ = plt.plot(pred.detach().cpu())

#%%
stim = batch['stim'].to(device)
model.eval()
model.convnet.use_checkpointing = False 
# %% try using jacrev
from scipy.ndimage import gaussian_filter
lag = 8
T,  C, H, W  = batch['stim'].shape
n_units      = len(good_units)
unit_ids     = torch.arange(n_units, device=device)
smooth_sigma = .5

# --------------------------------------------------------------------------
# 2. helper – Jacobian → energy CoM for *every* unit in one call
grid_y, grid_x = torch.meshgrid(torch.arange(H, device=device),
                                torch.arange(W, device=device),
                                indexing='ij')
grid_x = grid_x.expand(n_units, H, W)   # each unit gets the same grids
grid_y = grid_y.expand_as(grid_x)

def irf_J(frame_stim, unit_idx):
    """
    frame_stim : (C,H,W) tensor with grad
    returns     : (n_units, 2)   (cx, cy) per unit, NaN if IRF==0
    """
    def f(s):
        out = model(s.unsqueeze(0))[0]
        return out[unit_idx]

    return jacrev(f)(frame_stim)

def irf_com(frame_stim, unit_ids):
    """
    frame_stim : (C,H,W) tensor with grad
    returns     : (n_units, 2)   (cx, cy) per unit, NaN if IRF==0
    """
    J = irf_J(frame_stim, unit_ids)[:,lag]
    E = J.pow(2)

    if smooth_sigma:
        E = gaussian_filter(E.detach().cpu().numpy(),           # (n_units,H,W)
                            sigma=(0, smooth_sigma, smooth_sigma))
        E = torch.as_tensor(E, device=device)

    tot   = E.flatten(1).sum(-1)                              # (n_units,)
    mask  = tot > 0
    cx    = (E*grid_x).flatten(1).sum(-1) / tot
    cy    = (E*grid_y).flatten(1).sum(-1) / tot
    cx[~mask] = torch.nan
    cy[~mask] = torch.nan
    return torch.stack([cx, cy], 1)           # (n_units,2)

       # (n_units, C, H, W)

#%% --------------------------------------------------------------------------
# 3. pre-compute CoM for all frames *once*
com = torch.empty((T, n_units, 2), device=device)
with torch.set_grad_enabled(True):
    for t in tqdm(range(T)):
        com[t] = irf_com(stim[t], good_units)

com = com.detach().cpu().numpy()

from torch.func import jacrev, vmap
n_units = batch['robs'].shape[1]

#%%
%matplotlib widget
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D          # registers the 3-D projection

# --- pull out the data ---
T, n_units, _ = com.shape
xs = com[:, :, 0].copy()
ys = com[:, :, 1].copy()
# smooth xs and ys along axis 0
# xs = gaussian_filter(xs, sigma=(2.5,0))
# ys = gaussian_filter(ys, sigma=(2.5,0))
eyetraj = batch['eyepos'].clone().cpu().numpy()
eyetraj -= eyetraj[prewin*2]
ppd = 37
eyetraj *= ppd
eyetraj = -eyetraj
eyetraj[:,0] += xs.mean()
eyetraj[:,1] += ys.mean()
eyetraj = gaussian_filter(eyetraj, sigma=(.5,0))


ts = np.arange(T)                                # 0 … T-1
spikes = batch['robs'].cpu().numpy() > 0

fig = plt.figure(figsize=(6, 6))
ax  = fig.add_subplot(111, projection='3d')

# one colour per neuron
colors = plt.cm.hsv(np.linspace(0, 1, n_units, endpoint=False))

for i in range(n_units):
    # m = np.ones_like(spikes[:, i])
    m = spikes[:, i]                    # spike mask for this neuron
    if m.any():                         # skip silent cells
        ax.plot(ts[m], xs[m, i], ys[m, i], '-o',
                color=colors[i], lw=1, alpha=.25)  # line connects spike-only points
        
ax.plot(ts, eyetraj[:,0], eyetraj[:,1], color='k', lw=1, alpha=1)        
ax.plot(ts, xs.mean()*np.ones_like(ts), ys.mean()*np.ones_like(ts), color='k', lw=1, alpha=1) 


        # ax.scatter(ts[m], xs[m, i], ys[m, i],   # optional spike markers
                #    color=colors[i], s=6)
        
        # ax.plot(xs[m, i], ys[m, i], ts[m],
        #         color=colors[i], lw=1)  # line connects spike-only points
        # ax.scatter(xs[m, i], ys[m, i], ts[m],   # optional spike markers
        #            color=colors[i], s=6)

# axis labels (flip y if you want image-style origin at top-left)
ax.set_zlabel('X (px)')
ax.set_ylabel('Y (px)')
ax.set_xlabel('Frame')
ax.set_zlim(20, 40)
ax.set_ylim(20, 40)
ax.set_xlim(0, len(ts))
ax.invert_yaxis()

plt.tight_layout()
plt.show()

#%%
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import animation
from IPython.display import HTML
from matplotlib.cm import hsv

# ------------------------------------------------------------------
# assume you already have:
#   fix_batch    – dict with 'stim' (T,C,H,W) and 'robs' (T,N)
#   com          – tensor (T, N, 2)  center-of-mass per frame & neuron
#   spikes       – Boolean mask (T, N)  robs>0
# ------------------------------------------------------------------
T, n_units, _ = com.shape
stim_np = batch['stim'][:, lag].detach().cpu().numpy()      # (T,H,W)

# ----  figure & artists  -----------------------------------------
fig, ax = plt.subplots(figsize=(5, 5))
ax.axis("off")

stim_im = ax.imshow(stim_np[0], cmap='gray')

colors = hsv(np.linspace(0, 1, n_units, endpoint=False))

# one Line2D trail per neuron
trails = [ax.plot([], [], lw=1, color=colors[i], animated=True)[0]
          for i in range(n_units)]

# traj = ax.plot([], [], lw=1, color='r', animated=True)[0]

# moving dots (one PathCollection) for all neurons
# ---------------------------------------------------
# replace the old scatter line with this
xs0 = np.full(n_units, np.nan)
ys0 = np.full(n_units, np.nan)
dots = ax.scatter(xs0, ys0, s=8, c=colors, animated=True)

# dots = ax.scatter([], [], s=8, c=colors, animated=True)

# history buffers
hist_x = [[] for _ in range(n_units)]
hist_y = [[] for _ in range(n_units)]
hist_traj_x = []
hist_traj_y = []

spikes = batch['robs'].cpu().numpy() > 0

def update(t):
    # background stimulus
    stim_im.set_data(stim_np[t])

    xs, ys = [], []
    # hist_traj_x.append(eyetraj[t,0])
    # hist_traj_y.append(eyetraj[t,1])

    # # if len(hist_traj_x) > 4:
    # #     hist_traj_x = hist_traj_x[-2:]
    # #     hist_traj_y = hist_traj_y[-2:]
    # eye_ind = np.arange(np.m)
    
    # traj.set_data(eyetraj[range(max(t-2, 0), max(t,2))][0], eyetraj[range(max(t-2, 0), max(t,2))][1])

    for i in range(n_units):
        if spikes[t, i]:
            cx, cy = com[t, i].tolist()
            if not np.isnan(cx):
                hist_x[i].append(cx)
                hist_y[i].append(cy)
        # remove all but last n samples of hist_x[i] and hist_y[i]
        n = 4
        if len(hist_x[i]) > n:
            hist_x[i] = hist_x[i][-n:]
            hist_y[i] = hist_y[i][-n:]

        # update trail (may be empty)
        trails[i].set_data(hist_x[i], hist_y[i])

        # last point for the dot cloud (NaN → off-screen)
        if hist_x[i]:
            xs.append(hist_x[i][-1])
            ys.append(hist_y[i][-1])
        else:
            xs.append(np.nan)
            ys.append(np.nan)

    dots.set_offsets(np.c_[xs, ys])
    return (stim_im, dots, *trails)

ani = animation.FuncAnimation(fig, update,
                              frames=T, interval=50,
                              blit=True, repeat=False)

HTML(ani.to_jshtml())          # Jupyter preview

# # optional: save
# ani.save("all_neurons_trails.gif", writer="pillow", fps=20, dpi=100)
# print("saved all_neurons_trails.gif")

# %%
n_lags = stim.shape[1]
J = torch.empty((T, n_units, n_lags, H, W), device='cpu')
with torch.set_grad_enabled(True):
    for t in tqdm(range(T)):
        J[t] = irf_J(stim[t], good_units).detach().cpu()

#%%
%matplotlib inline
cc = 5
t = 11
plt.figure(figsize=(20,20))
for cc in range(n_units):
    for ilag in range(n_lags):
            plt.subplot(n_units, n_lags, cc*n_lags + ilag + 1)
            plt.imshow(J[t][cc][ilag], cmap='coolwarm', vmin=-J[t,cc].abs().max(), vmax=J[t,cc].abs().max())
            plt.axis('off')

# get rid of whitespace between subplots
plt.subplots_adjust(wspace=0, hspace=0)
# plt.imshow(J[0][15][1], cmap='coolwarm')
# plt.plot(com[0,10][0], com[0,10][1], 'ro')

#%%

# ---------- 1-panel figure ----------
fig, ax = plt.subplots(figsize=(5, 5))
ax.axis("off")

# ---------- overlay colormap ----------
overlay_cmap = plt.cm.get_cmap("coolwarm").copy()
overlay_cmap.set_bad(alpha=0)           # masked values → fully transparent

vmin, vmax = -J.abs().max(), J.abs().max()
ims = []

unit_id = [3] #list(range(n_units))
thresh = J.max().item()*.05
robs = batch['robs'].cpu()[:,good_units]

ims = []
spikes = batch['robs'].cpu() > 0
T = spikes.shape[0]
for t in range(T):
    nspikes = batch['robs'][t].sum()
    if nspikes == 0:
        nspikes = 1
    irf = torch.einsum('nhw, n->hw', J[t][unit_id][:,lag],robs[t][unit_id]) #/nspikes
    irf[irf.abs() < thresh] = 0.0001
    # background stimulus (gray)
    stim_img = ax.imshow(batch["stim"][t, 0].cpu(),
                         cmap="gray",
                         animated=True)

    # mask zeros so they don’t show up
    irf = np.ma.masked_equal(irf, 0)
    irf_img = ax.imshow(irf,
                        cmap=overlay_cmap,
                        vmin=vmin, vmax=vmax,
                        alpha=0.8,               # tune overall visibility
                        animated=True)

    ims.append([stim_img, irf_img])

ani = animation.ArtistAnimation(fig, ims,
                                interval=50, blit=True,
                                repeat_delay=1000)

# inline view in a notebook
HTML(ani.to_jshtml())

# optional: save to GIF (needs pillow only)
# ani.save("stim_irf_overlay.gif", writer="pillow", fps=20, dpi=100)
# print("saved stim_irf_overlay.gif")

# %%
# Compute IRFs for neurons when they spike and perform PCA
import torch.utils.checkpoint as checkpoint
from torch.linalg import pca_lowrank
import h5py

# Configuration
MAX_SAMPLES_PER_NEURON = 1000  # Limit samples per neuron to manage memory
INDIVIDUAL_PCA_DIMS = 5        # Components to keep for individual neurons
POPULATION_PCA_DIMS = 30       # Components to keep for population
OUTPUT_FILE = "irf_pca_subspaces.h5"

# Process one neuron at a time to manage memory
irfs_per_neuron = []
pca_per_neuron = []

print(f"Processing {len(good_units)} neurons...")
for i, unit_idx in enumerate(tqdm(good_units)):
    # Find spike times for this neuron
    spike_times = torch.where(batch['robs'][:, unit_idx] > 0)[0].cpu().numpy()
    
    # Limit number of samples if needed
    if len(spike_times) > MAX_SAMPLES_PER_NEURON:
        spike_times = np.random.choice(spike_times, MAX_SAMPLES_PER_NEURON, replace=False)
    
    if len(spike_times) == 0:
        print(f"No spikes for neuron {unit_idx}, skipping")
        irfs_per_neuron.append(None)
        pca_per_neuron.append(None)
        continue
    
    # Compute Jacobians for this neuron at spike times
    neuron_irfs = []
    with torch.set_grad_enabled(True):
        for t in spike_times:
            # Use checkpointing to reduce memory usage
            def get_jacobian(stimulus):
                def f(s):
                    return model(s.unsqueeze(0))[0, unit_idx]
                return jacrev(f)(stimulus)
            
            # Get Jacobian for specific lag (e.g., lag 8)
            jac = get_jacobian(stim[t])
            jac_lag = jac[lag].detach().cpu()
            neuron_irfs.append(jac_lag)
    
    # Stack IRFs and reshape for PCA
    if neuron_irfs:
        neuron_irfs_tensor = torch.stack(neuron_irfs)
        irfs_reshaped = neuron_irfs_tensor.reshape(neuron_irfs_tensor.shape[0], -1)
        
        # Perform PCA for this neuron
        U, S, V = pca_lowrank(irfs_reshaped, q=min(INDIVIDUAL_PCA_DIMS, irfs_reshaped.shape[0], irfs_reshaped.shape[1]))
        
        # Store results
        irfs_per_neuron.append(neuron_irfs_tensor)
        pca_per_neuron.append((U, S, V))
        
        print(f"Neuron {unit_idx}: {len(neuron_irfs)} IRFs, explained variance: {S.pow(2).sum() / irfs_reshaped.pow(2).sum().item():.2f}")
    else:
        irfs_per_neuron.append(None)
        pca_per_neuron.append(None)

# Compute population PCA across all neurons
print("Computing population PCA...")

# Collect all IRFs across neurons
all_irfs = []
for neuron_irfs in irfs_per_neuron:
    if neuron_irfs is not None:
        all_irfs.append(neuron_irfs)

if all_irfs:
    # Stack and reshape for PCA
    all_irfs_tensor = torch.cat(all_irfs, dim=0)
    all_irfs_reshaped = all_irfs_tensor.reshape(all_irfs_tensor.shape[0], -1)
    
    # Perform population PCA
    pop_U, pop_S, pop_V = pca_lowrank(all_irfs_reshaped, q=min(POPULATION_PCA_DIMS, all_irfs_reshaped.shape[0], all_irfs_reshaped.shape[1]))
    
    # Calculate explained variance
    explained_var = pop_S.pow(2).sum() / all_irfs_reshaped.pow(2).sum().item()
    print(f"Population PCA: {all_irfs_tensor.shape[0]} total IRFs, explained variance: {explained_var:.2f}")
else:
    print("No IRFs collected, skipping population PCA")
    pop_U, pop_S, pop_V = None, None, None

# Save results to HDF5 file
print(f"Saving results to {OUTPUT_FILE}")
with h5py.File(OUTPUT_FILE, 'w') as f:
    # Save good units
    f.create_dataset('good_units', data=good_units)
    
    # Create groups for individual neurons and population
    neuron_group = f.create_group('individual_neurons')
    pop_group = f.create_group('population')
    
    # Save individual neuron PCA results
    for i, unit_idx in enumerate(good_units):
        if pca_per_neuron[i] is not None:
            U, S, V = pca_per_neuron[i]
            neuron_subgroup = neuron_group.create_group(f'neuron_{unit_idx}')
            neuron_subgroup.create_dataset('U', data=U.numpy())
            neuron_subgroup.create_dataset('S', data=S.numpy())
            neuron_subgroup.create_dataset('V', data=V.numpy())
            neuron_subgroup.attrs['explained_variance'] = S.pow(2).sum().item() / irfs_per_neuron[i].reshape(-1).pow(2).sum().item()
    
    # Save population PCA results
    if pop_U is not None:
        pop_group.create_dataset('U', data=pop_U.numpy())
        pop_group.create_dataset('S', data=pop_S.numpy())
        pop_group.create_dataset('V', data=pop_V.numpy())
        pop_group.attrs['explained_variance'] = explained_var
        
        # Save the shape information for reconstructing IRFs
        pop_group.attrs['original_shape'] = np.array([H, W])

print("Done!")
