# ── DataYatesV1/models/modulator.py ────────────────────────────────────────────
"""
Modulators work on two inputs (features, behaviour)
which they combine into one output.

"""
import torch, torch.nn as nn
from typing import Optional, Tuple, Dict, Any

__all__ = ['BehaviorEncoder', 'ConcatModulator', 'FiLMModulator', 'BaseModulator']

# Dictionary mapping modulator types to their classes
MODULATORS = {
    'concat': ConcatModulator,
    'film': FiLMModulator
}

class BaseModulator(nn.Module):
    """Base class for configurable modulators."""
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.behavior_dim = config.get('behavior_dim', 0)
        self.out_dim = None  # Will be set by child classes
        self._build_modulator()
    
    def _build_modulator(self): 
        raise NotImplementedError
        
    def forward(self, feats: torch.Tensor, beh: torch.Tensor) -> torch.Tensor:
        raise NotImplementedError


