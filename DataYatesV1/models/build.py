"""
Model factory for DataYatesV1.
"""

import torch
import torch.nn as nn
from typing import Dict, Any
from DataYatesV1.utils.general import ensure_tensor

from .factory import (
    create_frontend, create_convnet, create_recurrent,
    create_modulator, create_readout
)

__all__ = ['build_model', 'initialize_model_components', 'get_name_from_config']

# Type aliases for clarity
ConfigDict = Dict[str, Any]

class ModularV1Model(nn.Module):
    """
    A modular V1 model architecture that allows easy swapping of components.

    The basic model architecture is as follows:
    stim (B, C_stim, T, H, W) ─► frontend ─► convcore ─► (B,C_conv,T,H,W) features
                                                |
                                behaviour  ─► MLP  (C_b)
                                                |
                                    concat (or FiLM) along channel dim
                                        (B,C_mod,T,H,W)
                                                ▼
                                            ConvGRU/LSTM
                                            (B,C_rec, T, H, W)
                                                ▼
                                            readout (factorised Gaussian or linear)
                                            (B, n_units)
                                                ▼
                                            activation (nn.Softplus()) ─► spikes
                                            

    This model is built from modular components that can be mixed and matched:
    - Frontend: Processes stimulus with a "front end" (da, conv, etc.) - "da" is the dynamic adaptation model from <PERSON> et al., 2013.
    - ConvNet: Feature extraction (DenseNet, CNN, ResNet, etc.)
    - Modulator: Behavioral modulation (MLP, Linear, etc.) - Optional, combines with convnet output before recurrent or readout.
    - Recurrent: Temporal processing (ConvLSTM, ConvGRU, etc.) - Optional
    - Readout: Output layer (DynamicGaussian, Linear, etc.)

    The model can be configured to skip certain components (like recurrent layers)
    by setting their type to 'none'.
    """
    def __init__(self, config: ConfigDict):
        """
        Initialize the model with the given configuration.

        Args:
            config: Dictionary containing model configuration
        """
        super().__init__()

        # Extract basic parameters
        self.height = config.get('height', None)
        self.width = config.get('width', None)
        self.sampling_rate = config.get('sampling_rate', 240)
        self.initial_input_channels = config.get('initial_input_channels', 1)

        # Set up activation function
        self.activation = config.get('output_activation', nn.Softplus())

        # Build the model components
        self._build_model(config)

        # Initialize bias if provided
        self._initialize_bias(config.get('init_rates'))

    def _build_model(self, config: ConfigDict, verbose=False):
        """Build all model components based on configuration."""
        # Track channel dimensions between components
        current_channels = self.initial_input_channels
        if verbose:
            print(f"Initial channels: {current_channels}")

        # get all configs
        frontend_config = config.get('frontend', {'type': 'da', 'params': {}})
        convnet_config = config.get('convnet', {'type': 'densenet', 'params': {}})
        modulator_config = config.get('modulator', {'type': 'none', 'mode': 'concatenate', 'params': {}})
        recurrent_config = config.get('recurrent', {'type': 'none', 'params': {}})
        readout_config = config.get('readout', {'type': 'gaussian', 'params': {}})
        
        # Build frontend
        frontend_type = frontend_config['type']
        frontend_params = frontend_config['params']
        self.frontend, current_channels = create_frontend(
            frontend_type=frontend_type,
            in_channels=current_channels,
            sampling_rate=self.sampling_rate,
            **frontend_params
        )

        # Build convnet
        if verbose:
            print(f"Current channels before convnet: {current_channels}")
        convnet_type = convnet_config['type']
        convnet_params = convnet_config['params']
        self.convnet, current_channels = create_convnet(
            convnet_type=convnet_type,
            in_channels=current_channels,
            **convnet_params
        )
        if verbose:
            print(f"Current channels after convnet: {current_channels}")

        # Build modulator (needed before recurrent to know dimensions)
        modulator_type = modulator_config['type']
        modulator_params = modulator_config['params']
        modulator_params['feature_dim'] = current_channels
        self.modulator, modulator_dim = create_modulator(
            modulator_type=modulator_type,
            **modulator_params
        )

        # Build recurrent
        recurrent_type = recurrent_config['type']
        recurrent_params = recurrent_config['params']

        # update input_dims depending on recurrent type
        if recurrent_type in ['lstm', 'gru']:
            assert readout_config['type'] == 'linear', 'LSTM/GRU only compatible with linear readout. There are no spatial dimensions'
            # error out with message
            raise NotImplementedError('LSTM/GRU used, flattening conv output. This is not supported yet.')
            # current_channels *= (self.height * self.width) # TODO: we need to somehow know the spatial output already

        # update for modulator
        if self.modulator is not None and modulator_dim > 0:
            # For concat modulators, add the modulator output channels
            # For FiLM modulators, channel count stays the same
            modulator_type = modulator_config.get('type', 'none')
            if modulator_type == 'concat':
                current_channels += modulator_dim
            elif modulator_type == 'film':
                # FiLM doesn't change channel count
                pass
            else:
                raise ValueError(f"Unknown modulator type: {modulator_type}")

        # create recurrent
        if verbose:
            print(f"Current channels before recurrent: {current_channels}")
        self.recurrent, current_channels = create_recurrent(
            recurrent_type=recurrent_type,
            input_dim=current_channels,
            **recurrent_params
        )

        # Build readout
        if verbose:
            print(f"Current channels before readout: {current_channels}")
        readout_type = readout_config['type']
        readout_params = readout_config['params']
        self.readout = create_readout(
            readout_type=readout_type,
            in_channels=current_channels,
            **readout_params
        )

    def _initialize_bias(self, init_rates):
        """Initialize readout bias based on initial firing rates."""
        if init_rates is not None and hasattr(self.readout, 'bias') and self.readout.bias is not None:
            print("Initializing readout bias.")
            assert len(init_rates) == self.readout.n_units, 'init_rates must have the same length as n_units'

            # Convert to tensor if needed
            if not isinstance(init_rates, torch.Tensor):
                init_rates = torch.tensor(init_rates, dtype=torch.float32, device=self.readout.bias.device)

            # Add small epsilon to prevent log(0)
            init_rates = init_rates + 1e-7

            # Apply inverse softplus to get bias values
            with torch.no_grad():
                self.readout.bias.data = torch.log(torch.exp(init_rates) - 1)
        elif init_rates is not None:
            print("Warning: init_rates provided but readout has no bias.")

    def forward(self, stimulus, behavior=None):
        """
        Forward pass through the model.

        Args:
            stimulus: Visual stimulus tensor with shape (N, C, T, H, W)
            behavior: Optional behavioral data with shape (N, n_vars)

        Returns:
            Tensor: Model predictions with shape (N, n_units)
        """

        # Process through frontend
        x = self.frontend(stimulus)
        # print(f"Frontend output shape: {x.shape}")

        # Process through convnet
        x_conv = self.convnet(x)
        # print(f"Convnet output shape: {x_conv.shape}")

        # Process through modulator
        if self.modulator is not None and behavior is not None:
            x_conv = self.modulator(x_conv, behavior)

        # pProcess through recurrent    
        x_recurrent = self.recurrent(x_conv)
        # print(f"Recurrent output shape: {x_recurrent.shape}")

        # Process through readout
        output = self.readout(x_recurrent)

        # Apply activation function
        output = self.activation(output)

        return output


def build_model(config: ConfigDict) -> nn.Module:
    """
    Build a model based on the provided configuration.

    This is the main entry point for creating models. It accepts a configuration
    dictionary that specifies the model architecture and parameters.

    Args:
        config: Dictionary containing model configuration
            - model_type: Type of model to build (default: 'v1')
            - frontend_type: Type of frontend ('da', 'conv', 'none')
            - convnet_type: Type of convnet ('densenet', 'conv', 'none')
            - recurrent_type: Type of recurrent layer ('convlstm', 'convgru', 'none')
            - modulator_type: Type of modulator ('lstm', 'linear', 'none')
            - readout_type: Type of readout ('gaussian', 'linear')
            - frontend_params: Parameters for frontend
            - convnet_params: Parameters for convnet
            - recurrent_params: Parameters for recurrent layer
            - modulator_params: Parameters for modulator
            - readout_params: Parameters for readout
            - output_activation: Activation function to apply to output
            - init_rates: Initial firing rates for readout bias initialization

    Returns:
        nn.Module: Constructed model
    """
    model_type = config.get('model_type', 'v1')

    if model_type == 'v1':
        return ModularV1Model(config)
    else:
        raise ValueError(f"Unknown model type: {model_type}")


def initialize_model_components(model: nn.Module, init_bias=None) -> None:
    """
    Initialize model components with appropriate weight initialization.

    Args:
        model: Model to initialize
    """
    
    # Initialize bias
    model._initialize_bias(init_bias)

    for name, module in model.named_modules():
        # DenseNet initialization
        if 'densenet' in name.lower() or 'convnet' in name.lower():
            if isinstance(module, nn.Conv2d) or isinstance(module, nn.Conv3d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(module, nn.BatchNorm2d) or isinstance(module, nn.BatchNorm3d) or isinstance(module, nn.LayerNorm):
                if hasattr(module, 'weight') and module.weight is not None:
                    nn.init.constant_(module.weight, 1.0)
                if hasattr(module, 'bias') and module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

        # Recurrent initialization
        elif 'convgru' in name.lower() or 'convlstm' in name.lower() or 'recurrent' in name.lower():
            if isinstance(module, nn.Conv2d):
                # Use Xavier/Glorot for recurrent connections
                nn.init.xavier_uniform_(module.weight)


def get_name_from_config(config):
    name = f"{config['model_type']}"
    for key in config.keys():
        if isinstance(config[key], dict):
            # and hasattr(config[key], 'type'):
            component = config[key]['type']
            name += f"_{component}"                
    
    return name
        
